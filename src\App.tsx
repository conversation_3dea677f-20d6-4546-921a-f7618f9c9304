import { Toaster } from "@/components/ui/Toaster";
import { Toaster as Sonner } from "@/components/ui/Sonner";
import { TooltipProvider } from "@/components/ui/Tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
  Outlet,
  useLocation,
} from "react-router-dom";
import { useAuth } from "./context/AuthContext";
import { useEffect, useRef } from "react";
import { ROUTES, ProtectedRouteWrapper } from "./routes/RouteConfig";
import LoadingSpinner from "./components/LoadingSpinner";
import { AuthProvider } from "./context/AuthContext";
import { refreshToken } from "@/utils/supabase/middleware";
import { useAuthStore } from "@/store/authStore";

// Import all page components
import Index from "./pages/Index";
import Login from "./pages/auth/Login";
import Register from "./pages/auth/Register";
import Articles from "./pages/resources/Articles";
import TutorSearch from "./pages/tutor/Search";
import BookSession from "./pages/sessions/BookDemo";
import StudentDashboard from "./pages/student/Dashboard";
import StudentProfile from "./pages/student/Profile";
import StudentSchedulePage from "./pages/student/Schedule";
import StudentJourney from "./pages/student/Journey";
import StudentMaterials from "./pages/student/Materials";
import RequestBooking from "./pages/student/RequestBooking";
import Products from "@/pages/student/Products";
import BillingHistory from "@/pages/student/BillingHistory";
import Subscriptions from "@/pages/student/Subscriptions";
import AccountPreferences from "@/pages/student/AccountPreferences";
import NotificationsPage from "@/pages/student/Notifications";
import Inquiry from "./pages/Inquiry";
import Feedback from "./pages/Feedback";
import BecomeTutor from "./pages/BecomeTutor";
import NotFound from "./pages/NotFound";
import Inquiries from "./pages/admin/Inquiries";
import AdminDashboard from "./pages/admin/Dashboard";
import ForgotPassword from "@/pages/auth/ForgotPassword";
import ForgotPasswordConfirmation from "@/pages/auth/ForgotPasswordConfirmation";
import ResetPassword from "@/pages/auth/ResetPassword";
import TutorManagement from "@/pages/admin/TutorManagement";
import UserManagement from "@/pages/admin/UserManagement";
import Terms from "@/pages/Terms";
import Privacy from "@/pages/Privacy";
import HowItWorks from "@/pages/HowItWorks";
import Resources from "@/pages/Resources";
import About from "@/pages/About";
import Courses from "@/pages/Courses";
import Careers from "@/pages/Careers";
import Pricing from "@/pages/Pricing";
import Tutors from "@/pages/Tutors";
import Contact from "@/pages/Contact";
import ScrollToTop from "@/components/ScrollToTop";
import SessionManagement from "./pages/admin/SessionManagement";
import ScheduleSession from "./pages/admin/sessions/ScheduleSession";
import EditSession from "./pages/admin/sessions/EditSession";
import JoinSession from "./pages/sessions/JoinSession";
import AddUsersToSession from "./pages/admin/sessions/AddUsersToSession";
import RemoveUserFromSession from "./pages/admin/sessions/RemoveUserFromSession";
import SessionsList from "./pages/admin/sessions/SessionsList";
import SessionAnalytics from "./pages/admin/sessions/SessionAnalytics";
import BatchManagement from "@/pages/admin/BatchManagement";
import CreateBatch from "@/pages/admin/batches/CreateBatch";
import ViewBatches from "@/pages/admin/batches/ViewBatches";
import EditBatch from "@/pages/admin/batches/EditBatch";
import DeleteBatch from "@/pages/admin/batches/DeleteBatch";
import ConfirmEmail from "./pages/auth/ConfirmEmail";
import ConfirmationCallback from "./components/auth/ConfirmationCallback";
import UnderMaintenance from "./pages/UnderMaintenance";
import StudentOnboarding from "./pages/student/Onboarding";
import TutorOnboarding from "./pages/tutor/Onboarding";
import NewSubscription from "./pages/student/NewSubscription";
import NewSubscriptionSelect from "./pages/student/NewSubscriptionSelect";
import NewSubscriptionConfigure from "./pages/student/NewSubscriptionConfigure";
import NewSubscriptionPricing from "./pages/student/NewSubscriptionPricing";
import NewSubscriptionPurchase from "./pages/student/NewSubscriptionPurchase";
import TutorDashboard from "./pages/tutor/Dashboard";
import AllSessions from "./pages/tutor/sessions/AllSessions";
import UpcomingSessions from "./pages/tutor/sessions/UpcomingSessions";
import PastSessions from "./pages/tutor/sessions/PastSessions";
import StudentOverview from "./pages/tutor/students/StudentOverview";
import Performance from "./pages/tutor/students/Performance";
import TutorProfilePage from "./pages/tutor/Profile";
// Import the Schedule page directly instead of lazy loading
import TutorSchedulePage from "./pages/tutor/Schedule";
import PendingRequests from "./pages/tutor/requests/PendingRequests";
import AcceptedRequests from "./pages/tutor/requests/AcceptedRequests";
import RejectionRequests from "./pages/tutor/requests/RejectionRequests";
// Import profile related pages
import EditProfilePage from "./pages/tutor/profile/edit";
import ResourcesPage from "./pages/tutor/profile/resources";
import TutorBookSessionsPage from "./pages/tutor/BookSessions";
import TutorAccountPreferences from "./pages/tutor/AccountPreferences";
import TutorNotifications from "./pages/tutor/Notifications";
import PhotoUploadTest from "./pages/PhotoUploadTest";
import TestSVG from "./pages/TestSVG";
import TestHero from "./components/TestHero";
import StudentUpcomingSessions from "./pages/student/sessions/UpcomingSessions";
import StudentPastSessions from "./pages/student/sessions/PastSessions";
import AcceptSession from "./pages/student/AcceptSession";



// Map of route paths to components
const ROUTE_COMPONENTS = {
  "/": Index,
  "/register": Register,
  "/forgot-password": ForgotPassword,
  "/forgot-password-confirmation": ForgotPasswordConfirmation,
  "/reset-password": ResetPassword,
  "/articles": Articles,
  "/tutor/search": TutorSearch,
  "/book-session/:tutorId": BookSession,
  "/inquiry": Inquiry,
  "/feedback": Feedback,
  "/terms": Terms,
  "/privacy": Privacy,
  "/auth/callback": ConfirmationCallback,

  "/maintenance": UnderMaintenance,
  "/admin/inquiries": Inquiries,
  "/admin/tutors": TutorManagement,
  "/admin/users": UserManagement,
  "/admin/sessions": SessionManagement,
  "/admin/sessions/schedule": ScheduleSession,
  "/admin/sessions/edit": EditSession,
  "/admin/sessions/add-users": AddUsersToSession,
  "/admin/sessions/remove-user": RemoveUserFromSession,
  "/admin/sessions/list": SessionsList,
  "/admin/sessions/analytics": SessionAnalytics,
  "/tutor/dashboard": TutorDashboard,
  "/tutor/sessions/all": AllSessions,
  "/tutor/sessions/upcoming": UpcomingSessions,
  "*": NotFound,
};

// Simplified AppRoutes component using RouteConfig and Zustand
const AppRoutes = () => {
  // Use Zustand store directly
  const {
    user,
    userType,
    isOnboarded,
    isInitialized,
    isLoading,
    userStatus,
    session,
    isUserAdmin,
  } = useAuthStore();

  // Log auth state for debugging
  useEffect(() => {
    console.log("Auth state in AppRoutes:", {
      user: user ? `User ${user.id}` : "null",
      isAdmin: isUserAdmin(),
      isLoading,
      isInitialized,
      userType,
      isOnboarded,
      userStatus,
      session: session ? "Active" : "None",
    });
  }, [
    user,
    isUserAdmin,
    isLoading,
    isInitialized,
    userType,
    isOnboarded,
    userStatus,
    session,
  ]);

  // Move this useEffect outside of any conditional rendering
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (!useAuthStore.getState().isInitialized) {
        console.warn("Loading timeout in AppRoutes - forcing initialization");
        useAuthStore.getState().setIsInitialized(true);
      }
    }, 3000);

    return () => clearTimeout(timeout);
  }, []);

  // If not initialized, show loading spinner
  if (!isInitialized) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <LoadingSpinner />
        <p className="mt-4 text-gray-600">Loading application...</p>
      </div>
    );
  }

  return (
    <Routes>
      {/* Public routes */}
      <Route path={ROUTES.HOME.path} element={<Index />} />
      <Route path={ROUTES.LOGIN.path} element={<Login />} />
      <Route path={ROUTES.REGISTER.path} element={<Register />} />
      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route
        path="/forgot-password-confirmation"
        element={<ForgotPasswordConfirmation />}
      />
      <Route path="/reset-password" element={<ResetPassword />} />
      <Route path="/articles" element={<Articles />} />
      <Route path="/tutor/search" element={<TutorSearch />} />
      <Route path="/book-session/:tutorId" element={<BookSession />} />
      <Route path="/inquiry" element={<Inquiry />} />
      <Route path="/feedback" element={<Feedback />} />

      {/* Session joining route - accessible to all authenticated users */}
      <Route
        path={ROUTES.JOIN_SESSION.path}
        element={
          <ProtectedRouteWrapper routeConfig={ROUTES.JOIN_SESSION}>
            <JoinSession />
          </ProtectedRouteWrapper>
        }
      />
      <Route path={ROUTES.BECOME_TUTOR.path} element={<BecomeTutor />} />
      <Route path="/terms" element={<Terms />} />
      <Route path="/privacy" element={<Privacy />} />
      <Route path={ROUTES.HOW_IT_WORKS.path} element={<HowItWorks />} />
      <Route path={ROUTES.RESOURCES.path} element={<Resources />} />
      <Route path={ROUTES.ABOUT.path} element={<About />} />
      <Route path={ROUTES.COURSES.path} element={<Courses />} />
      <Route path={ROUTES.CAREERS.path} element={<Careers />} />
      <Route path={ROUTES.PRICING.path} element={<Pricing />} />
      <Route path={ROUTES.TUTORS.path} element={<Tutors />} />
      <Route path={ROUTES.CONTACT.path} element={<Contact />} />
      <Route path={ROUTES.CONFIRM_EMAIL.path} element={<ConfirmEmail />} />
      <Route path="/auth/callback" element={<ConfirmationCallback />} />

      <Route path="/photo-upload-test" element={<PhotoUploadTest />} />
      <Route path="/testSVG" element={<TestSVG />} />
      <Route path="/test-hero" element={<TestHero />} />

      <Route
        path="/maintenance"
        element={
          <UnderMaintenance
            estimatedTime="2 hours"
            message="We're upgrading our systems to serve you better. Please check back soon."
          />
        }
      />

      {/* Onboarding routes */}
      <Route
        element={
          <ProtectedRouteWrapper routeConfig={ROUTES.STUDENT_ONBOARDING}>
            <Outlet />
          </ProtectedRouteWrapper>
        }
      >
        <Route
          path={ROUTES.STUDENT_ONBOARDING.path}
          element={<StudentOnboarding />}
        />
      </Route>

      <Route
        element={
          <ProtectedRouteWrapper routeConfig={ROUTES.TUTOR_ONBOARDING}>
            <Outlet />
          </ProtectedRouteWrapper>
        }
      >
        <Route
          path={ROUTES.TUTOR_ONBOARDING.path}
          element={<TutorOnboarding />}
        />
      </Route>

      {/* Admin routes */}
      <Route
        element={
          <ProtectedRouteWrapper routeConfig={ROUTES.ADMIN_DASHBOARD}>
            <Outlet />
          </ProtectedRouteWrapper>
        }
      >
        <Route
          path={ROUTES.ADMIN_DASHBOARD.path}
          element={<AdminDashboard />}
        />
        <Route path="/admin/inquiries" element={<Inquiries />} />
        <Route path="/admin/tutors" element={<TutorManagement />} />
        <Route path="/admin/users" element={<UserManagement />} />
        <Route path="/admin/sessions" element={<SessionManagement />} />
        <Route path="/admin/sessions/schedule" element={<ScheduleSession />} />
        <Route path="/admin/sessions/edit" element={<EditSession />} />
        <Route
          path="/admin/sessions/add-users"
          element={<AddUsersToSession />}
        />
        <Route
          path="/admin/sessions/remove-user"
          element={<RemoveUserFromSession />}
        />
        <Route path="/admin/sessions/list" element={<SessionsList />} />
        <Route
          path="/admin/sessions/analytics"
          element={<SessionAnalytics />}
        />
        <Route
          path={ROUTES.ADMIN_BATCHES.path}
          element={<BatchManagement />}
        />
        <Route
          path={ROUTES.ADMIN_CREATE_BATCH.path}
          element={<CreateBatch />}
        />
        <Route
          path={ROUTES.ADMIN_VIEW_BATCHES.path}
          element={<ViewBatches />}
        />
        <Route
          path={ROUTES.ADMIN_EDIT_BATCH.path}
          element={<EditBatch />}
        />
        <Route
          path={ROUTES.ADMIN_DELETE_BATCH.path}
          element={<DeleteBatch />}
        />
      </Route>

      {/* Tutor routes */}
      <Route
        element={
          <ProtectedRouteWrapper
            routeConfig={{
              path: ROUTES.TUTOR_DASHBOARD.path,
              requiresAuth: true,
              allowedUserTypes: ["tutor"],
              requiresOnboarding: true,
              fallbackPath: "/",
            }}
          >
            <Outlet />
          </ProtectedRouteWrapper>
        }
      >
        <Route path={ROUTES.TUTOR_DASHBOARD.path} element={<TutorDashboard />} />
        <Route path={ROUTES.TUTOR_PROFILE.path} element={<TutorProfilePage/>} />
        <Route path="/tutor/profile/edit" element={<EditProfilePage />} />
        <Route path="/tutor/profile/resources" element={<ResourcesPage />} />
        <Route path={ROUTES.TUTOR_SCHEDULE.path} element={<TutorSchedulePage />} />
        <Route path={ROUTES.TUTOR_BOOK_SESSIONS.path} element={<TutorBookSessionsPage />} />
        <Route path={ROUTES.TUTOR_ACCOUNT_PREFERENCES.path} element={<TutorAccountPreferences />} />
        <Route path={ROUTES.TUTOR_NOTIFICATIONS.path} element={<TutorNotifications />} />
        <Route path={ROUTES.TUTOR_PENDING_REQUESTS.path} element={<PendingRequests />} />
        <Route path={ROUTES.TUTOR_ACCEPTED_REQUESTS.path} element={<AcceptedRequests />} />
        <Route path={ROUTES.TUTOR_REJECTION_REQUESTS.path} element={<RejectionRequests />} />
        <Route path="/tutor/sessions/all" element={<AllSessions />} />
        <Route path="/tutor/sessions/upcoming" element={<UpcomingSessions />} />
        <Route path={ROUTES.TUTOR_PAST_SESSIONS.path} element={<PastSessions />} />
        <Route path={ROUTES.TUTOR_STUDENT_OVERVIEW.path} element={<StudentOverview />} />
        <Route path={ROUTES.TUTOR_STUDENT_PERFORMANCE.path} element={<Performance />} />
      </Route>
      {/* Student routes */}
      <Route
        element={
          <ProtectedRouteWrapper routeConfig={ROUTES.STUDENT_DASHBOARD}>
            <Outlet />
          </ProtectedRouteWrapper>
        }
      >
        <Route
          path={ROUTES.STUDENT_DASHBOARD.path}
          element={<StudentDashboard />}
        />
        <Route
          path={ROUTES.STUDENT_PROFILE.path}
          element={<StudentProfile />}
        />
        <Route
          path={ROUTES.STUDENT_SCHEDULE.path}
          element={<StudentSchedulePage />}
        />
        <Route
          path={ROUTES.STUDENT_JOURNEY.path}
          element={<StudentJourney />}
        />
        <Route
          path={ROUTES.STUDENT_MATERIALS.path}
          element={<StudentMaterials />}
        />
        <Route
          path={ROUTES.STUDENT_REQUEST_BOOKING.path}
          element={<RequestBooking />}
        />
        <Route
          path={ROUTES.STUDENT_ACCEPT_SESSION.path}
          element={<AcceptSession />}
        />
        <Route
          path={ROUTES.STUDENT_PRODUCTS.path}
          element={<Products />}
        />
        <Route
          path={ROUTES.STUDENT_BILLING_HISTORY.path}
          element={<BillingHistory />}
        />
        <Route
          path={ROUTES.STUDENT_SUBSCRIPTIONS.path}
          element={<Subscriptions />}
        />
        <Route
          path={ROUTES.STUDENT_NEW_SUBSCRIPTION.path}
          element={<NewSubscription />}
        />
        <Route
          path={ROUTES.STUDENT_NEW_SUBSCRIPTION_SELECT.path}
          element={<NewSubscriptionSelect />}
        />
        <Route
          path={ROUTES.STUDENT_NEW_SUBSCRIPTION_CONFIGURE.path}
          element={<NewSubscriptionConfigure />}
        />
        <Route
          path={ROUTES.STUDENT_NEW_SUBSCRIPTION_PRICING.path}
          element={<NewSubscriptionPricing />}
        />
        <Route
          path={ROUTES.STUDENT_NEW_SUBSCRIPTION_PURCHASE.path}
          element={<NewSubscriptionPurchase />}
        />
        <Route
          path={ROUTES.STUDENT_ACCOUNT_PREFERENCES.path}
          element={<AccountPreferences />}
        />
        <Route
          path={ROUTES.STUDENT_NOTIFICATIONS.path}
          element={<NotificationsPage />}
        />
        <Route
          path={ROUTES.STUDENT_UPCOMING_SESSIONS.path}
          element={<StudentUpcomingSessions />}
        />
        <Route
          path={ROUTES.STUDENT_PAST_SESSIONS.path}
          element={<StudentPastSessions />}
        />
      </Route>

      {/* Catch-all route */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

const queryClient = new QueryClient();

const App = () => {
  console.log("App component rendering");
  const isMounted = useRef(true);

  // Check for persisted navigation state - with safer cleanup
  useEffect(() => {
    // Only run if component is mounted
    if (!isMounted.current) return;

    try {
      const persistedNavigation = sessionStorage.getItem(
        "retry_login_navigation"
      );
      if (persistedNavigation) {
        console.log("Found persisted navigation state:", persistedNavigation);
      }
    } catch (error) {
      console.error("Error checking persisted navigation:", error);
    }

    // Cleanup function
    return () => {
      isMounted.current = false;
    };
  }, []); // Empty dependency array for mount/unmount only

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Router>
            <ScrollToTop />
            <AppRoutes />
            <Toaster />
            <Sonner />
          </Router>
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};

export default App;
