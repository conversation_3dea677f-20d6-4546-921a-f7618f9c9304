import React from "react";

const HeroSVGPair: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className="flex w-full h-full" style={{
      paddingRight: '8px', // sm: 8px to match navbar mx-2
      gap: '8px'
    }}>
      {/* Hero Left SVG */}
      <div style={{ width: '50%', height: '100%' }}>
        <img
          src="/svg/heroLeft.svg"
          alt="Hero Left Background"
          style={{ width: '100%', height: '100%', objectFit: 'cover' }}
        />
      </div>

      {/* Hero Right SVG */}
      <div style={{ width: '50%', height: '100%' }}>
        <img
          src="/svg/heroRight.svg"
          alt="Hero Right Background"
          style={{ width: '100%', height: '100%', objectFit: 'cover' }}
        />
      </div>
    </div>
  );
};

// Add responsive padding to match navbar margins
const HeroSVGPairWithResponsivePadding: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className="w-full h-full pr-2 sm:pr-4 md:pr-9">
      <HeroSVGPair className={className} />
    </div>
  );
};

export default HeroSVGPairWithResponsivePadding;

export default HeroSVGPair;
